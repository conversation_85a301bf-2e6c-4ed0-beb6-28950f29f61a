<template>
  <PageContainer
    title="微信登录权限管理"
    subtitle="管理可以通过微信扫码登录后台的用户权限"
    :breadcrumb="breadcrumbItems"
  >
    <template #header-actions>
      <BaseButton
        variant="primary"
        prefix-icon="PlusIcon"
        @click="showAddModal = true"
      >
        添加权限
      </BaseButton>
    </template>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="总权限数"
        :value="stats.totalPermissions"
        :icon="ShieldCheckIcon"
        color="blue"
        description="已授权用户"
      />
      <StatsCard
        title="有效权限"
        :value="stats.activePermissions"
        :icon="CheckCircleIcon"
        color="green"
        description="当前有效"
      />
      <StatsCard
        title="即将过期"
        :value="stats.expiringPermissions"
        :icon="ExclamationTriangleIcon"
        color="yellow"
        description="7天内过期"
      />
      <StatsCard
        title="今日登录"
        :value="stats.todayLogins"
        :icon="UserCheckIcon"
        color="purple"
        description="微信登录次数"
      />
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchParams"
      :fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 权限列表 -->
    <BaseTable
      :data="permissions"
      :columns="tableColumns"
      :selectable="true"
      v-model:selected-rows="selectedPermissions"
      :loading="loading"
      @row-click="handleRowClick"
      @sort-change="handleSort"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            权限列表 ({{ pagination.total }})
          </h3>
          <div class="flex items-center space-x-3">
            <BaseButton
              v-if="selectedPermissions.length"
              variant="error"
              size="sm"
              @click="handleBatchRevoke"
            >
              批量撤销 ({{ selectedPermissions.length }})
            </BaseButton>
            <BaseButton
              variant="outline"
              size="sm"
              @click="handleExport"
            >
              导出数据
            </BaseButton>
          </div>
        </div>
      </template>

      <!-- 用户信息 -->
      <template #cell-user="{ row }">
        <div class="flex items-center">
          <img
            v-if="row.wechatUser?.avatar"
            :src="row.wechatUser.avatar"
            :alt="row.wechatUser.nickname"
            class="h-8 w-8 rounded-full object-cover mr-2"
          />
          <div
            v-else
            class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-2"
          >
            <UserIcon class="h-4 w-4 text-gray-500" />
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ row.wechatUser?.nickname || '未知用户' }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ row.openid.slice(0, 8) }}...
            </div>
          </div>
        </div>
      </template>

      <!-- 权限级别 -->
      <template #cell-permissionLevel="{ row }">
        <span
          :class="getPermissionLevelClass(row.permissionLevel)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getPermissionLevelText(row.permissionLevel) }}
        </span>
      </template>

      <!-- 状态 -->
      <template #cell-status="{ row }">
        <span
          :class="getStatusClass(row)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getStatusText(row) }}
        </span>
      </template>

      <!-- 授权人 -->
      <template #cell-grantedBy="{ row }">
        <div v-if="row.grantedByUser" class="text-sm">
          <div class="font-medium text-gray-900 dark:text-white">
            {{ row.grantedByUser.username }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatDate(row.grantedAt) }}
          </div>
        </div>
        <span v-else class="text-sm text-gray-500 dark:text-gray-400">
          系统授权
        </span>
      </template>

      <!-- 过期时间 -->
      <template #cell-expiresAt="{ row }">
        <div v-if="row.expiresAt" class="text-sm">
          <div class="text-gray-900 dark:text-white">
            {{ formatDate(row.expiresAt) }}
          </div>
          <div
            :class="getExpiryWarningClass(row.expiresAt)"
            class="text-xs"
          >
            {{ getExpiryText(row.expiresAt) }}
          </div>
        </div>
        <span v-else class="text-sm text-green-600 dark:text-green-400">
          永不过期
        </span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleView(row)"
          >
            查看
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleEdit(row)"
            class="text-blue-600"
          >
            编辑
          </BaseButton>
          <BaseButton
            v-if="row.status === 1"
            variant="ghost"
            size="sm"
            @click="handleRevoke(row)"
            class="text-red-600 hover:text-red-700"
          >
            撤销
          </BaseButton>
          <BaseButton
            v-else
            variant="ghost"
            size="sm"
            @click="handleRestore(row)"
            class="text-green-600 hover:text-green-700"
          >
            恢复
          </BaseButton>
        </div>
      </template>

      <!-- 分页 -->
      <template #footer>
        <Pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </template>
    </BaseTable>

    <!-- 添加权限模态框 -->
    <AddPermissionModal
      v-model="showAddModal"
      @success="handleAddSuccess"
    />

    <!-- 编辑权限模态框 -->
    <EditPermissionModal
      v-model="showEditModal"
      :permission="currentPermission"
      @success="handleEditSuccess"
    />

    <!-- 权限详情模态框 -->
    <PermissionDetailModal
      v-model="showDetailModal"
      :permission="currentPermission"
    />
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import {
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserCheckIcon,
  UserIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import AddPermissionModal from './components/AddPermissionModal.vue'
import EditPermissionModal from './components/EditPermissionModal.vue'
import PermissionDetailModal from './components/PermissionDetailModal.vue'
import { wechatPermissionApi } from '@/api/wechatPermission'
import { formatDate } from '@/utils/date'
import { useConfirm } from '@/composables/useConfirm'
import { useToast } from '@/composables/useToast'

const { confirm } = useConfirm()
const { success, error } = useToast()

// 响应式数据
const loading = ref(false)
const permissions = ref([])
const selectedPermissions = ref([])
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDetailModal = ref(false)
const currentPermission = ref(null)

// 搜索参数
const searchParams = reactive({
  keyword: '',
  permissionLevel: '',
  status: '',
  expiryStatus: '',
  dateRange: []
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  totalPermissions: 0,
  activePermissions: 0,
  expiringPermissions: 0,
  todayLogins: 0
})

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '系统管理', to: '/system' },
  { text: '微信权限管理' }
]

// 搜索字段配置
const searchFields = [
  {
    key: 'keyword',
    type: 'input',
    label: '关键词',
    placeholder: '搜索用户昵称、OpenID',
    prefixIcon: 'MagnifyingGlassIcon'
  },
  {
    key: 'permissionLevel',
    type: 'select',
    label: '权限级别',
    placeholder: '请选择权限级别',
    options: [
      { label: '全部', value: '' },
      { label: '超级管理员', value: 'super_admin' },
      { label: '管理员', value: 'admin' },
      { label: '操作员', value: 'operator' }
    ]
  },
  {
    key: 'status',
    type: 'select',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '有效', value: '1' },
      { label: '已撤销', value: '0' }
    ]
  },
  {
    key: 'expiryStatus',
    type: 'select',
    label: '过期状态',
    placeholder: '请选择过期状态',
    options: [
      { label: '全部', value: '' },
      { label: '永不过期', value: 'never' },
      { label: '即将过期', value: 'expiring' },
      { label: '已过期', value: 'expired' }
    ]
  },
  {
    key: 'dateRange',
    type: 'daterange',
    label: '授权时间',
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期'
  }
]

// 表格列配置
const tableColumns = [
  {
    key: 'user',
    title: '用户信息'
  },
  {
    key: 'permissionLevel',
    title: '权限级别'
  },
  {
    key: 'status',
    title: '状态'
  },
  {
    key: 'grantedBy',
    title: '授权信息'
  },
  {
    key: 'expiresAt',
    title: '过期时间',
    sortable: true
  }
]

// 计算属性和方法
const getPermissionLevelClass = (level) => {
  const classes = {
    super_admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    admin: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    operator: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[level] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getPermissionLevelText = (level) => {
  const texts = {
    super_admin: '超级管理员',
    admin: '管理员',
    operator: '操作员'
  }
  return texts[level] || '未知'
}

const getStatusClass = (permission) => {
  if (permission.status === 0) {
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  
  if (permission.expiresAt && new Date(permission.expiresAt) < new Date()) {
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
  
  return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
}

const getStatusText = (permission) => {
  if (permission.status === 0) {
    return '已撤销'
  }
  
  if (permission.expiresAt && new Date(permission.expiresAt) < new Date()) {
    return '已过期'
  }
  
  return '有效'
}

const getExpiryWarningClass = (expiresAt) => {
  const now = new Date()
  const expiry = new Date(expiresAt)
  const daysLeft = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))
  
  if (daysLeft <= 0) {
    return 'text-red-600 dark:text-red-400'
  } else if (daysLeft <= 7) {
    return 'text-yellow-600 dark:text-yellow-400'
  } else {
    return 'text-green-600 dark:text-green-400'
  }
}

const getExpiryText = (expiresAt) => {
  const now = new Date()
  const expiry = new Date(expiresAt)
  const daysLeft = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24))
  
  if (daysLeft <= 0) {
    return '已过期'
  } else if (daysLeft <= 7) {
    return `${daysLeft}天后过期`
  } else {
    return '正常'
  }
}

// 数据获取方法
const fetchPermissions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams
    }
    
    const response = await wechatPermissionApi.getPermissions(params)
    permissions.value = response.data.permissions
    pagination.total = response.data.total
  } catch (err) {
    console.error('获取权限列表失败:', err)
    error('获取权限列表失败')
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    const response = await wechatPermissionApi.getStats()
    Object.assign(stats, response.data)
  } catch (err) {
    console.error('获取统计数据失败:', err)
  }
}

// 事件处理方法
const handleSearch = () => {
  pagination.current = 1
  fetchPermissions()
}

const handleReset = () => {
  pagination.current = 1
  fetchPermissions()
}

const handlePageChange = () => {
  fetchPermissions()
}

const handleSort = (sortInfo) => {
  console.log('排序:', sortInfo)
  fetchPermissions()
}

const handleView = (permission) => {
  currentPermission.value = permission
  showDetailModal.value = true
}

const handleEdit = (permission) => {
  currentPermission.value = permission
  showEditModal.value = true
}

const handleRevoke = async (permission) => {
  try {
    await confirm({
      title: '确认撤销权限',
      content: `确定要撤销用户 ${permission.wechatUser?.nickname || '未知用户'} 的登录权限吗？`,
      type: 'warning'
    })
    
    await wechatPermissionApi.revokePermission(permission.id)
    success('权限撤销成功')
    fetchPermissions()
    fetchStats()
  } catch (err) {
    if (err.message !== 'User cancelled') {
      error('撤销权限失败')
    }
  }
}

const handleRestore = async (permission) => {
  try {
    await wechatPermissionApi.restorePermission(permission.id)
    success('权限恢复成功')
    fetchPermissions()
    fetchStats()
  } catch (err) {
    error('恢复权限失败')
  }
}

const handleBatchRevoke = async () => {
  try {
    await confirm({
      title: '确认批量撤销',
      content: `确定要撤销选中的 ${selectedPermissions.value.length} 个权限吗？`,
      type: 'warning'
    })
    
    const permissionIds = selectedPermissions.value.map(p => p.id)
    await wechatPermissionApi.batchRevokePermissions(permissionIds)
    
    success('批量撤销成功')
    selectedPermissions.value = []
    fetchPermissions()
    fetchStats()
  } catch (err) {
    if (err.message !== 'User cancelled') {
      error('批量撤销失败')
    }
  }
}

const handleExport = () => {
  console.log('导出数据')
}

const handleRowClick = (permission) => {
  console.log('点击权限:', permission)
}

const handleAddSuccess = () => {
  showAddModal.value = false
  fetchPermissions()
  fetchStats()
}

const handleEditSuccess = () => {
  showEditModal.value = false
  fetchPermissions()
  fetchStats()
}

// 生命周期
onMounted(() => {
  fetchPermissions()
  fetchStats()
})
</script>
