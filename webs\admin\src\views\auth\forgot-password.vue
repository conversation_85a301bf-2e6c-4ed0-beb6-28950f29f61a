<template>
  <div class="forgot-password-container">
    <div class="forgot-password-form">
      <div class="form-header">
        <h2>忘记密码</h2>
        <p>输入您的邮箱地址，我们将发送重置密码链接</p>
      </div>

      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
        label-width="0"
        size="large"
      >
        <el-form-item prop="email">
          <el-input
            v-model="forgotForm.email"
            placeholder="请输入注册邮箱"
            prefix-icon="Message"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleForgotPassword"
            style="width: 100%"
          >
            发送重置链接
          </el-button>
        </el-form-item>

        <div class="form-footer">
          <el-link type="primary" @click="$router.push('/login')">
            返回登录
          </el-link>
          <span class="divider">|</span>
          <el-link type="primary" @click="$router.push('/register')">
            注册账户
          </el-link>
        </div>
      </el-form>

      <!-- 发送成功提示 -->
      <div v-if="emailSent" class="success-message">
        <el-icon class="success-icon"><CircleCheck /></el-icon>
        <h3>邮件已发送</h3>
        <p>
          我们已向 <strong>{{ forgotForm.email }}</strong> 发送了重置密码链接
        </p>
        <p>请检查您的邮箱（包括垃圾邮件文件夹）</p>
        <el-button type="primary" @click="resendEmail" :loading="loading">
          重新发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, CircleCheck } from '@element-plus/icons-vue'

const router = useRouter()
const forgotFormRef = ref()
const loading = ref(false)
const emailSent = ref(false)

const forgotForm = reactive({
  email: ''
})

const forgotRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const handleForgotPassword = async () => {
  try {
    await forgotFormRef.value.validate()
    loading.value = true

    // TODO: 调用忘记密码API
    // const response = await authApi.forgotPassword(forgotForm.email)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    emailSent.value = true
    ElMessage.success('重置密码邮件已发送')
  } catch (error) {
    console.error('发送失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}

const resendEmail = async () => {
  loading.value = true
  try {
    // TODO: 重新发送邮件
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('邮件已重新发送')
  } catch (error) {
    ElMessage.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.forgot-password-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.divider {
  margin: 0 10px;
  color: #ddd;
}

.success-message {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.success-message h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 20px;
}

.success-message p {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

.success-message strong {
  color: #333;
}
</style>
