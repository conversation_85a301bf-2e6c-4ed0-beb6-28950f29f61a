const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const {auth} = require('../middlewares/auth');

// 登录
router.post('/login', authController.login);

// 注册
router.post('/register', authController.register);

// 获取当前用户信息
router.get('/me', auth, authController.getMe);

// 登出
router.post('/logout', auth, authController.logout);

// 刷新令牌
router.post('/refresh', authController.refreshToken);

module.exports = router;
