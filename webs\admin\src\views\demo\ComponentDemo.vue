<template>
  <PageContainer
    title="组件演示"
    subtitle="展示所有自定义组件的使用方法"
    :breadcrumb="breadcrumbItems"
  >
    <!-- 基础组件演示 -->
    <div class="space-y-8">
      <!-- 表单组件 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">表单组件</h3>
        </div>
        <div class="card-body space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 输入框 -->
            <BaseInput
              v-model="form.username"
              label="用户名"
              placeholder="请输入用户名"
              :prefix-icon="UserIcon"
              required
              clearable
              help-text="用户名长度为3-20个字符"
            />
            
            <!-- 密码输入框 -->
            <BaseInput
              v-model="form.password"
              type="password"
              label="密码"
              placeholder="请输入密码"
              :prefix-icon="LockClosedIcon"
              required
            />
            
            <!-- 选择器 -->
            <BaseSelect
              v-model="form.role"
              label="用户角色"
              placeholder="请选择角色"
              :options="roleOptions"
            />
            
            <!-- 日期选择器 -->
            <BaseDatePicker
              v-model="form.birthday"
              label="生日"
              placeholder="请选择生日"
            />
            
            <!-- 日期范围选择器 -->
            <div class="md:col-span-2">
              <BaseDateRangePicker
                v-model="form.dateRange"
                label="时间范围"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </div>
            
            <!-- 文本域 -->
            <div class="md:col-span-2">
              <BaseTextarea
                v-model="form.description"
                label="描述"
                placeholder="请输入描述信息"
                :rows="4"
                :max-length="200"
                show-count
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 文件上传 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">文件上传</h3>
        </div>
        <div class="card-body space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 单文件上传 -->
            <BaseUpload
              v-model="form.avatar"
              label="头像上传"
              accept="image/*"
              :multiple="false"
              help-text="支持 JPG、PNG 格式，文件大小不超过 2MB"
            />
            
            <!-- 多文件上传 -->
            <BaseUpload
              v-model="form.attachments"
              label="附件上传"
              :multiple="true"
              :max-files="5"
              help-text="最多上传 5 个文件"
            />
          </div>
        </div>
      </div>

      <!-- 按钮组件 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">按钮组件</h3>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <!-- 按钮变体 -->
            <div class="flex flex-wrap gap-3">
              <BaseButton variant="primary">主要按钮</BaseButton>
              <BaseButton variant="secondary">次要按钮</BaseButton>
              <BaseButton variant="success">成功按钮</BaseButton>
              <BaseButton variant="warning">警告按钮</BaseButton>
              <BaseButton variant="error">错误按钮</BaseButton>
              <BaseButton variant="outline">边框按钮</BaseButton>
              <BaseButton variant="ghost">幽灵按钮</BaseButton>
              <BaseButton variant="link">链接按钮</BaseButton>
            </div>
            
            <!-- 按钮尺寸 -->
            <div class="flex flex-wrap items-center gap-3">
              <BaseButton size="xs">超小按钮</BaseButton>
              <BaseButton size="sm">小按钮</BaseButton>
              <BaseButton size="md">中等按钮</BaseButton>
              <BaseButton size="lg">大按钮</BaseButton>
              <BaseButton size="xl">超大按钮</BaseButton>
            </div>
            
            <!-- 带图标的按钮 -->
            <div class="flex flex-wrap gap-3">
              <BaseButton :prefix-icon="PlusIcon">新增</BaseButton>
              <BaseButton :suffix-icon="ArrowRightIcon">下一步</BaseButton>
              <BaseButton :loading="buttonLoading" @click="handleButtonClick">
                {{ buttonLoading ? '加载中...' : '点击加载' }}
              </BaseButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 反馈组件 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">反馈组件</h3>
        </div>
        <div class="card-body space-y-6">
          <!-- 警告框 -->
          <div class="space-y-4">
            <BaseAlert
              type="success"
              title="操作成功"
              description="您的操作已成功完成。"
              closable
            />
            <BaseAlert
              type="warning"
              title="注意"
              description="这是一个警告信息，请注意查看。"
              closable
            />
            <BaseAlert
              type="error"
              title="错误"
              description="操作失败，请检查输入信息。"
              closable
            />
            <BaseAlert
              type="info"
              title="提示"
              description="这是一个信息提示。"
              closable
            />
          </div>
          
          <!-- 加载状态 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded">
              <LoadingSpinner type="spinner" />
              <p class="text-sm text-gray-500 mt-2">旋转加载</p>
            </div>
            <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded">
              <LoadingSpinner type="dots" />
              <p class="text-sm text-gray-500 mt-2">点状加载</p>
            </div>
            <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded">
              <LoadingSpinner type="bars" />
              <p class="text-sm text-gray-500 mt-2">条状加载</p>
            </div>
            <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded">
              <LoadingSpinner type="pulse" />
              <p class="text-sm text-gray-500 mt-2">脉冲加载</p>
            </div>
          </div>
          
          <!-- 交互按钮 -->
          <div class="flex flex-wrap gap-3">
            <BaseButton @click="showToast('success')">成功提示</BaseButton>
            <BaseButton @click="showToast('error')">错误提示</BaseButton>
            <BaseButton @click="showToast('warning')">警告提示</BaseButton>
            <BaseButton @click="showToast('info')">信息提示</BaseButton>
            <BaseButton @click="showConfirm">确认对话框</BaseButton>
            <BaseButton @click="showDeleteConfirm">删除确认</BaseButton>
          </div>
        </div>
      </div>

      <!-- 数据展示组件 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">数据展示组件</h3>
        </div>
        <div class="card-body space-y-6">
          <!-- 统计卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="总用户数"
              :value="1234"
              :icon="UsersIcon"
              color="blue"
              :change="12.5"
              description="较上月增长"
            />
            <StatsCard
              title="今日订单"
              :value="56"
              :icon="ShoppingCartIcon"
              color="green"
              :change="-5.2"
              description="较昨日"
            />
            <StatsCard
              title="总收入"
              :value="98765"
              :icon="CurrencyDollarIcon"
              color="purple"
              :change="8.9"
              description="较上月增长"
              :format-options="{ style: 'currency', currency: 'CNY' }"
            />
            <StatsCard
              title="活跃用户"
              :value="789"
              :icon="UserCheckIcon"
              color="yellow"
              description="本周活跃"
            />
          </div>
          
          <!-- 空状态 -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
            <EmptyState
              title="暂无数据"
              description="当前没有任何数据，点击下方按钮添加数据"
              action-text="添加数据"
              :action-icon="PlusIcon"
              @action="handleAddData"
            />
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {
  UserIcon,
  LockClosedIcon,
  PlusIcon,
  ArrowRightIcon,
  UsersIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon,
  UserCheckIcon
} from '@heroicons/vue/24/outline'
import { useToast } from '@/composables/useToast'
import { useConfirm } from '@/composables/useConfirm'

// 组合式函数
const { success, error, warning, info } = useToast()
const { confirm, delete: deleteConfirm } = useConfirm()

// 响应式数据
const buttonLoading = ref(false)

const form = reactive({
  username: '',
  password: '',
  role: '',
  birthday: '',
  dateRange: [],
  description: '',
  avatar: [],
  attachments: []
})

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '演示页面', to: '/demo' },
  { text: '组件演示' }
]

// 选项数据
const roleOptions = [
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' },
  { label: '访客', value: 'guest' }
]

// 方法
const handleButtonClick = () => {
  buttonLoading.value = true
  setTimeout(() => {
    buttonLoading.value = false
    success('加载完成！')
  }, 2000)
}

const showToast = (type) => {
  const messages = {
    success: '操作成功！',
    error: '操作失败，请重试。',
    warning: '请注意检查输入信息。',
    info: '这是一条信息提示。'
  }
  
  const toastFunctions = { success, error, warning, info }
  toastFunctions[type](messages[type])
}

const showConfirm = async () => {
  try {
    await confirm({
      title: '确认操作',
      content: '确定要执行此操作吗？',
      type: 'warning'
    })
    success('操作已确认！')
  } catch {
    info('操作已取消。')
  }
}

const showDeleteConfirm = async () => {
  try {
    await deleteConfirm('这个项目')
    success('删除成功！')
  } catch {
    info('删除已取消。')
  }
}

const handleAddData = () => {
  info('添加数据功能待实现')
}
</script>
